# Firebase Storage Security Rules Configuration

## Issue
You're getting "object does not exist at location" error when uploading images. This is typically caused by incorrect Firebase Storage security rules or configuration.

## Required Firebase Storage Security Rules

Go to your Firebase Console → Storage → Rules and replace the existing rules with:

```javascript
rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow authenticated users to upload and read their own profile images
    match /profile_images/{userId}.jpg {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Allow authenticated users to upload and read message images
    match /message_images/{imageId} {
      allow read, write: if request.auth != null;
    }
    
    // Allow read access to all profile images for discovery feature
    match /profile_images/{allImages=**} {
      allow read: if request.auth != null;
    }
    
    // Test path for debugging (remove in production)
    match /test/{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Steps to Fix the Issue

### 1. Update Firebase Storage Rules
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `chatme-70d24`
3. Go to Storage → Rules
4. Replace the existing rules with the rules above
5. Click "Publish"

### 2. Verify Firebase Storage is Enabled
1. In Firebase Console, go to Storage
2. Make sure Storage is enabled for your project
3. Verify the storage bucket is: `chatme-70d24.firebasestorage.app`

### 3. Check Authentication
1. Make sure the user is properly authenticated before uploading
2. Verify the user's authentication token is valid

### 4. Test the Upload
1. Run the app with the updated code
2. Create a new account or login
3. Try uploading a profile image
4. Check the Android Studio Logcat for detailed debug information

## Debug Information

The updated code now includes detailed logging. Look for these log messages in Android Studio Logcat:

```
SpotMe: Starting image upload for user: [USER_ID]
SpotMe: Storage verification: [VERIFICATION_RESULT]
SpotMe: Storage reference path: profile_images/[USER_ID].jpg
SpotMe: Storage bucket: chatme-70d24.firebasestorage.app
```

If you see any errors, they will be logged with detailed information about what went wrong.

## Common Causes of "Object Does Not Exist" Error

1. **Incorrect Security Rules**: The most common cause
2. **Storage not enabled**: Firebase Storage service not activated
3. **Authentication issues**: User not properly authenticated
4. **Network connectivity**: Poor internet connection
5. **Bucket configuration**: Wrong storage bucket in configuration

## Alternative Storage Rules (More Permissive for Testing)

If the above rules don't work, try these more permissive rules for testing:

```javascript
rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    match /{allPaths=**} {
      allow read, write: if request.auth != null;
    }
  }
}
```

**Note**: These permissive rules should only be used for testing. Use the specific rules above for production.

## Verification Steps

After updating the rules:

1. Clean and rebuild your Android project
2. Uninstall and reinstall the app
3. Create a new account
4. Try uploading an image
5. Check the logs for detailed error information

If the issue persists, check the Firebase Console → Storage → Files to see if any files are being created, even if the download URL fails.
