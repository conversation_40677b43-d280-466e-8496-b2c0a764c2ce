package com.example.spotme.data.repository

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.location.Location
import androidx.core.content.ContextCompat
import com.example.spotme.data.model.User
import com.example.spotme.data.model.UserLocation
import com.google.android.gms.location.*
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.GeoPoint
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

class LocationRepository(private val context: Context) {
    private val auth = FirebaseAuth.getInstance()
    private val firestore = FirebaseFirestore.getInstance()
    private val fusedLocationClient = LocationServices.getFusedLocationProviderClient(context)
    
    private val usersCollection = firestore.collection("users")
    
    fun hasLocationPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            context,
            Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
    }
    
    suspend fun getCurrentLocation(): Result<Location> {
        return try {
            if (!hasLocationPermission()) {
                throw SecurityException("Permission de localisation non accordée")
            }

            println("SpotMe: Tentative d'obtention de la localisation...")

            // Essayer d'abord la dernière localisation connue
            val lastLocation = fusedLocationClient.lastLocation.await()
            if (lastLocation != null) {
                println("SpotMe: Localisation obtenue depuis le cache: ${lastLocation.latitude}, ${lastLocation.longitude}")
                Result.success(lastLocation)
            } else {
                println("SpotMe: Aucune localisation en cache, demande d'une nouvelle localisation...")

                // Si pas de localisation en cache, demander une nouvelle localisation
                val locationRequest = com.google.android.gms.location.LocationRequest.Builder(
                    com.google.android.gms.location.Priority.PRIORITY_HIGH_ACCURACY,
                    10000L // 10 secondes
                ).apply {
                    setMinUpdateIntervalMillis(5000L) // 5 secondes minimum
                    setMaxUpdateDelayMillis(15000L) // 15 secondes maximum
                }.build()

                // Utiliser une approche simplifiée pour obtenir la localisation
                try {
                    val currentLocation = fusedLocationClient.getCurrentLocation(
                        com.google.android.gms.location.Priority.PRIORITY_HIGH_ACCURACY,
                        null
                    ).await()

                    if (currentLocation != null) {
                        println("SpotMe: Nouvelle localisation obtenue: ${currentLocation.latitude}, ${currentLocation.longitude}")
                        Result.success(currentLocation)
                    } else {
                        throw Exception("Impossible d'obtenir la localisation actuelle. Vérifiez que le GPS est activé.")
                    }
                } catch (e: Exception) {
                    println("SpotMe: Erreur lors de l'obtention de la localisation: ${e.message}")
                    throw Exception("Erreur de localisation: ${e.message}. Assurez-vous que le GPS est activé et que vous êtes à l'extérieur.")
                }
            }
        } catch (e: SecurityException) {
            println("SpotMe: Erreur de permission: ${e.message}")
            Result.failure(Exception("Permission de localisation requise. Veuillez autoriser l'accès à la localisation dans les paramètres."))
        } catch (e: Exception) {
            println("SpotMe: Erreur générale de localisation: ${e.message}")
            Result.failure(e)
        }
    }
    
    fun startLocationUpdates(): Flow<Location> = callbackFlow {
        if (!hasLocationPermission()) {
            close(SecurityException("Location permission not granted"))
            return@callbackFlow
        }
        
        val locationRequest = LocationRequest.Builder(
            Priority.PRIORITY_HIGH_ACCURACY,
            30000L // 30 seconds
        ).apply {
            setMinUpdateDistanceMeters(50f) // Update when moved 50 meters
            setMaxUpdateDelayMillis(60000L) // Max 1 minute delay
        }.build()
        
        val locationCallback = object : LocationCallback() {
            override fun onLocationResult(result: LocationResult) {
                result.lastLocation?.let { location ->
                    trySend(location)
                }
            }
        }
        
        try {
            fusedLocationClient.requestLocationUpdates(
                locationRequest,
                locationCallback,
                null
            )
        } catch (e: SecurityException) {
            close(e)
        }
        
        awaitClose {
            fusedLocationClient.removeLocationUpdates(locationCallback)
        }
    }
    
    suspend fun findNearbyUsers(currentLocation: GeoPoint, radiusInMeters: Double = 300.0): Result<List<User>> {
        return try {
            val currentUserId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            
            // Calculate bounding box for the query
            val latRange = radiusInMeters / 111320.0 // Approximate meters per degree latitude
            val lonRange = radiusInMeters / (111320.0 * kotlin.math.cos(Math.toRadians(currentLocation.latitude)))
            
            val minLat = currentLocation.latitude - latRange
            val maxLat = currentLocation.latitude + latRange
            val minLon = currentLocation.longitude - lonRange
            val maxLon = currentLocation.longitude + lonRange
            
            // Query users within bounding box (simplified to avoid index requirements)
            val querySnapshot = usersCollection
                .whereEqualTo("isVisible", true)
                .get()
                .await()
            
            val nearbyUsers = querySnapshot.documents.mapNotNull { document ->
                val user = document.toObject(User::class.java)?.copy(id = document.id)
                user?.takeIf { 
                    it.id != currentUserId && 
                    it.location != null &&
                    UserLocation.calculateDistance(
                        currentLocation.latitude,
                        currentLocation.longitude,
                        it.location!!.latitude,
                        it.location!!.longitude
                    ) <= radiusInMeters
                }
            }
            
            Result.success(nearbyUsers)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun updateUserLocation(location: Location): Result<Unit> {
        return try {
            val userId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            val geoPoint = GeoPoint(location.latitude, location.longitude)
            
            usersCollection.document(userId).update(
                mapOf(
                    "location" to geoPoint,
                    "lastLocationUpdate" to com.google.firebase.Timestamp.now()
                )
            ).await()
            
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
