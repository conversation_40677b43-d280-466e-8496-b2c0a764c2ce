package com.example.spotme.ui.screen

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.example.spotme.ui.viewmodel.AuthViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    onSignOut: () -> Unit,
    authViewModel: AuthViewModel = viewModel()
) {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    
    Scaffold(
        bottomBar = {
            NavigationBar {
                bottomNavItems.forEach { item ->
                    NavigationBarItem(
                        icon = { 
                            Icon(
                                imageVector = item.icon,
                                contentDescription = item.title
                            )
                        },
                        label = { Text(item.title) },
                        selected = currentRoute == item.route,
                        onClick = {
                            if (currentRoute != item.route) {
                                navController.navigate(item.route) {
                                    // Pop up to the start destination to avoid building up a large stack
                                    popUpTo(navController.graph.startDestinationId) {
                                        saveState = true
                                    }
                                    // Avoid multiple copies of the same destination
                                    launchSingleTop = true
                                    // Restore state when reselecting a previously selected item
                                    restoreState = true
                                }
                            }
                        }
                    )
                }
            }
        }
    ) { paddingValues ->
        NavHost(
            navController = navController,
            startDestination = BottomNavItem.Discovery.route,
            modifier = Modifier.padding(paddingValues)
        ) {
            composable(BottomNavItem.Discovery.route) {
                DiscoveryTabScreen(
                    onUserClick = { user ->
                        navController.navigate("user_profile/${user.id}")
                    }
                )
            }
            
            composable(BottomNavItem.Profile.route) {
                ProfileTabScreen(
                    onSignOut = onSignOut
                )
            }
            
            composable(BottomNavItem.Messages.route) {
                MessagesTabScreen(
                    onChatClick = { chatId ->
                        navController.navigate("chat/$chatId")
                    }
                )
            }
            
            // Detail screens
            composable("user_profile/{userId}") { backStackEntry ->
                val userId = backStackEntry.arguments?.getString("userId") ?: ""
                UserProfileScreen(
                    userId = userId,
                    onNavigateBack = {
                        navController.popBackStack()
                    },
                    onStartChat = { otherUserId ->
                        navController.navigate("chat/$otherUserId")
                    }
                )
            }
            
            composable("chat/{otherUserId}") { backStackEntry ->
                val otherUserId = backStackEntry.arguments?.getString("otherUserId") ?: ""
                ChatScreen(
                    otherUserId = otherUserId,
                    onNavigateBack = {
                        navController.popBackStack()
                    }
                )
            }
        }
    }
}

// Bottom Navigation Items
sealed class BottomNavItem(
    val route: String,
    val title: String,
    val icon: ImageVector
) {
    object Discovery : BottomNavItem(
        route = "discovery_tab",
        title = "Carte",
        icon = Icons.Default.LocationOn
    )
    
    object Profile : BottomNavItem(
        route = "profile_tab", 
        title = "Profil",
        icon = Icons.Default.Person
    )
    
    object Messages : BottomNavItem(
        route = "messages_tab",
        title = "Messages", 
        icon = Icons.Default.Message
    )
}

val bottomNavItems = listOf(
    BottomNavItem.Discovery,
    BottomNavItem.Profile,
    BottomNavItem.Messages
)
