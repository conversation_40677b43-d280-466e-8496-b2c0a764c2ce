package com.example.spotme.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.spotme.data.model.Message
import com.example.spotme.data.model.MessageType
import com.example.spotme.data.repository.ChatRepository
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class ChatDetailViewModel(
    private val chatRepository: ChatRepository = ChatRepository()
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(ChatDetailUiState())
    val uiState: StateFlow<ChatDetailUiState> = _uiState.asStateFlow()
    
    private val _messages = MutableStateFlow<List<Message>>(emptyList())
    val messages: StateFlow<List<Message>> = _messages.asStateFlow()
    
    private var currentChatId: String? = null
    
    fun loadMessages(otherUserId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true, error = null)
                
                // Get or create chat
                val chatResult = chatRepository.getOrCreateChat(otherUserId)
                if (chatResult.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = chatResult.exceptionOrNull()?.message ?: "Erreur lors de la création du chat"
                    )
                    return@launch
                }
                
                val chat = chatResult.getOrThrow()
                currentChatId = chat.id
                
                // Observe messages
                chatRepository.observeMessages(chat.id)
                    .catch { exception ->
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = exception.message ?: "Erreur lors du chargement des messages"
                        )
                    }
                    .collect { messageList ->
                        _messages.value = messageList
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = null
                        )
                    }
                    
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = e.message ?: "Erreur lors du chargement des messages"
                )
            }
        }
    }
    
    fun sendMessage(otherUserId: String, content: String) {
        val chatId = currentChatId ?: return
        
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isSending = true, error = null)
                
                val result = chatRepository.sendMessage(
                    chatId = chatId,
                    receiverId = otherUserId,
                    content = content,
                    type = MessageType.TEXT
                )
                
                if (result.isFailure) {
                    _uiState.value = _uiState.value.copy(
                        isSending = false,
                        error = result.exceptionOrNull()?.message ?: "Erreur lors de l'envoi du message"
                    )
                } else {
                    _uiState.value = _uiState.value.copy(isSending = false)
                }
                
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    isSending = false,
                    error = e.message ?: "Erreur lors de l'envoi du message"
                )
            }
        }
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
}

data class ChatDetailUiState(
    val isLoading: Boolean = false,
    val isSending: Boolean = false,
    val error: String? = null
)
