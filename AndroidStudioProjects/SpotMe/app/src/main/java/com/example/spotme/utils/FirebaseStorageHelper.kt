package com.example.spotme.utils

import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.tasks.await

object FirebaseStorageHelper {
    
    /**
     * Verify Firebase Storage configuration and connectivity
     */
    suspend fun verifyStorageConfiguration(): Result<String> {
        return try {
            val auth = FirebaseAuth.getInstance()
            val storage = FirebaseStorage.getInstance()
            
            // Check if user is authenticated
            val currentUser = auth.currentUser
            if (currentUser == null) {
                return Result.failure(Exception("User not authenticated"))
            }
            
            println("SpotMe: Storage verification - User authenticated: ${currentUser.uid}")
            
            // Get storage reference
            val storageRef = storage.reference
            println("SpotMe: Storage verification - Storage reference: ${storageRef.bucket}")
            
            // Try to create a test reference (this doesn't upload anything)
            val testRef = storageRef.child("test/connection_test.txt")
            println("SpotMe: Storage verification - Test reference path: ${testRef.path}")
            
            // Check if we can access the storage bucket
            val metadata = try {
                storageRef.metadata.await()
                "Storage accessible"
            } catch (e: Exception) {
                println("SpotMe: Storage verification - Metadata check failed: ${e.message}")
                "Storage metadata not accessible: ${e.message}"
            }
            
            Result.success("Storage configuration verified. $metadata")
            
        } catch (e: Exception) {
            println("SpotMe: Storage verification failed: ${e.message}")
            Result.failure(e)
        }
    }
    
    /**
     * Get detailed storage information for debugging
     */
    fun getStorageInfo(): String {
        return try {
            val storage = FirebaseStorage.getInstance()
            val auth = FirebaseAuth.getInstance()
            
            buildString {
                appendLine("=== Firebase Storage Debug Info ===")
                appendLine("Storage bucket: ${storage.reference.bucket}")
                appendLine("Storage reference: ${storage.reference}")
                appendLine("Auth user: ${auth.currentUser?.uid ?: "Not authenticated"}")
                appendLine("Auth email: ${auth.currentUser?.email ?: "No email"}")
                appendLine("Max upload retry time: ${storage.maxUploadRetryTimeMillis}")
                appendLine("Max download retry time: ${storage.maxDownloadRetryTimeMillis}")
                appendLine("Max operation retry time: ${storage.maxOperationRetryTimeMillis}")
            }
        } catch (e: Exception) {
            "Error getting storage info: ${e.message}"
        }
    }
}
