package com.example.spotme.ui.viewmodel

import android.content.Context
import android.location.Location
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.spotme.data.model.Gender
import com.example.spotme.data.model.User
import com.example.spotme.data.repository.LocationRepository
import com.example.spotme.data.repository.UserRepository
import com.google.firebase.firestore.GeoPoint
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch

class DiscoveryViewModel(
    context: Context,
    private val locationRepository: LocationRepository = LocationRepository(context),
    private val userRepository: UserRepository = UserRepository()
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(DiscoveryUiState())
    val uiState: StateFlow<DiscoveryUiState> = _uiState.asStateFlow()
    
    private val _currentLocation = MutableStateFlow<Location?>(null)
    val currentLocation: StateFlow<Location?> = _currentLocation.asStateFlow()
    
    private val _nearbyUsers = MutableStateFlow<List<User>>(emptyList())
    val nearbyUsers: StateFlow<List<User>> = _nearbyUsers.asStateFlow()
    
    // Filtered users based on current filters
    val filteredUsers: StateFlow<List<User>> = combine(
        _nearbyUsers,
        _uiState
    ) { users, state ->
        users.filter { user ->
            // Filter by gender if specified
            if (state.genderFilter != null && user.gender != state.genderFilter) {
                return@filter false
            }
            
            // Filter by age range
            val age = user.getAge()
            if (age < state.minAge || age > state.maxAge) {
                return@filter false
            }
            
            // Filter by interests if specified
            if (state.interestFilter.isNotEmpty()) {
                val hasMatchingInterest = user.interests.any { interest ->
                    state.interestFilter.any { filter ->
                        interest.contains(filter, ignoreCase = true)
                    }
                }
                if (!hasMatchingInterest) {
                    return@filter false
                }
            }
            
            true
        }.sortedBy { user ->
            // Sort by distance if current location is available
            _currentLocation.value?.let { currentLoc ->
                user.location?.let { userLoc ->
                    val userLocation = Location("").apply {
                        latitude = userLoc.latitude
                        longitude = userLoc.longitude
                    }
                    currentLoc.distanceTo(userLocation)
                }
            } ?: 0f
        }
    }.stateIn(
        scope = viewModelScope,
        started = SharingStarted.WhileSubscribed(5000),
        initialValue = emptyList()
    )
    
    init {
        startLocationTracking()
    }
    
    private fun startLocationTracking() {
        if (!locationRepository.hasLocationPermission()) {
            _uiState.value = _uiState.value.copy(
                error = "Location permission required to discover nearby users"
            )
            return
        }
        
        viewModelScope.launch {
            locationRepository.startLocationUpdates()
                .catch { error ->
                    _uiState.value = _uiState.value.copy(
                        error = error.message ?: "Failed to get location updates"
                    )
                }
                .collect { location ->
                    _currentLocation.value = location
                    updateUserLocation(location)
                    findNearbyUsers(location)
                }
        }
    }
    
    private suspend fun updateUserLocation(location: Location) {
        val result = locationRepository.updateUserLocation(location)
        if (result.isFailure) {
            _uiState.value = _uiState.value.copy(
                error = result.exceptionOrNull()?.message ?: "Failed to update location"
            )
        }
    }
    
    private suspend fun findNearbyUsers(location: Location) {
        _uiState.value = _uiState.value.copy(isLoading = true)
        
        val geoPoint = GeoPoint(location.latitude, location.longitude)
        val result = locationRepository.findNearbyUsers(geoPoint)
        
        if (result.isSuccess) {
            _nearbyUsers.value = result.getOrThrow()
            _uiState.value = _uiState.value.copy(isLoading = false)
        } else {
            _uiState.value = _uiState.value.copy(
                isLoading = false,
                error = result.exceptionOrNull()?.message ?: "Failed to find nearby users"
            )
        }
    }
    
    fun refreshNearbyUsers() {
        val location = _currentLocation.value
        if (location != null) {
            viewModelScope.launch {
                findNearbyUsers(location)
            }
        } else {
            getCurrentLocation()
        }
    }
    
    fun getCurrentLocation() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true)
            
            val result = locationRepository.getCurrentLocation()
            
            if (result.isSuccess) {
                val location = result.getOrThrow()
                _currentLocation.value = location
                updateUserLocation(location)
                findNearbyUsers(location)
            } else {
                val errorMessage = result.exceptionOrNull()?.message ?: "Impossible d'obtenir la localisation"
                println("SpotMe: Erreur de localisation dans ViewModel: $errorMessage")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = errorMessage
                )
            }
        }
    }
    
    fun setGenderFilter(gender: Gender?) {
        _uiState.value = _uiState.value.copy(genderFilter = gender)
    }
    
    fun setAgeRange(minAge: Int, maxAge: Int) {
        _uiState.value = _uiState.value.copy(minAge = minAge, maxAge = maxAge)
    }
    
    fun setInterestFilter(interests: List<String>) {
        _uiState.value = _uiState.value.copy(interestFilter = interests)
    }
    
    fun clearFilters() {
        _uiState.value = _uiState.value.copy(
            genderFilter = null,
            minAge = 18,
            maxAge = 100,
            interestFilter = emptyList()
        )
    }
    
    fun setViewMode(viewMode: ViewMode) {
        _uiState.value = _uiState.value.copy(viewMode = viewMode)
    }
    
    fun clearError() {
        _uiState.value = _uiState.value.copy(error = null)
    }
    
    fun getDistanceToUser(user: User): String {
        val currentLoc = _currentLocation.value
        val userLoc = user.location

        if (currentLoc == null || userLoc == null) {
            return "Distance inconnue"
        }

        val userLocation = Location("").apply {
            latitude = userLoc.latitude
            longitude = userLoc.longitude
        }

        val distance = currentLoc.distanceTo(userLocation)

        return when {
            distance < 1000 -> "${distance.toInt()}m"
            else -> "${"%.1f".format(distance / 1000)}km"
        }
    }
}

data class DiscoveryUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val viewMode: ViewMode = ViewMode.LIST,
    val genderFilter: Gender? = null,
    val minAge: Int = 18,
    val maxAge: Int = 100,
    val interestFilter: List<String> = emptyList()
)

enum class ViewMode {
    LIST, MAP
}
