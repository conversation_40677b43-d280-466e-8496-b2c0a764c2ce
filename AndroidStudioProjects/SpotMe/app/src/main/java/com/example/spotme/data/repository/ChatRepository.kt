package com.example.spotme.data.repository

import android.net.Uri
import com.example.spotme.data.model.Chat
import com.example.spotme.data.model.Message
import com.example.spotme.data.model.MessageType
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.google.firebase.storage.FirebaseStorage
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import java.util.*

class ChatRepository {
    private val auth = FirebaseAuth.getInstance()
    private val firestore = FirebaseFirestore.getInstance()
    private val storage = FirebaseStorage.getInstance()
    
    private val chatsCollection = firestore.collection("chats")
    private val messagesCollection = firestore.collection("messages")
    
    suspend fun createOrGetChat(otherUserId: String): Result<Chat> {
        return try {
            val currentUserId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            val participants = listOf(currentUserId, otherUserId).sorted()
            val chatId = participants.joinToString("_")
            
            // Check if chat already exists
            val existingChat = chatsCollection.document(chatId).get().await()
            
            if (existingChat.exists()) {
                val chat = existingChat.toObject(Chat::class.java)?.copy(id = existingChat.id)
                    ?: throw Exception("Failed to parse chat")
                Result.success(chat)
            } else {
                // Create new chat
                val newChat = Chat(
                    id = chatId,
                    participants = participants,
                    createdAt = Date()
                )
                chatsCollection.document(chatId).set(newChat).await()
                Result.success(newChat)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun sendMessage(
        chatId: String,
        receiverId: String,
        content: String,
        type: MessageType = MessageType.TEXT,
        imageUri: Uri? = null
    ): Result<Message> {
        return try {
            val currentUserId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            
            var imageUrl = ""
            if (type == MessageType.IMAGE && imageUri != null) {
                val uploadResult = uploadMessageImage(imageUri)
                if (uploadResult.isFailure) {
                    throw uploadResult.exceptionOrNull() ?: Exception("Image upload failed")
                }
                imageUrl = uploadResult.getOrThrow()
            }
            
            val message = Message(
                id = UUID.randomUUID().toString(),
                chatId = chatId,
                senderId = currentUserId,
                receiverId = receiverId,
                content = content,
                imageUrl = imageUrl,
                type = type,
                timestamp = Date()
            )
            
            // Save message
            messagesCollection.document(message.id).set(message).await()
            
            // Update chat with last message
            chatsCollection.document(chatId).update(
                mapOf(
                    "lastMessage" to message,
                    "lastMessageTimestamp" to message.timestamp
                )
            ).await()
            
            Result.success(message)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private suspend fun uploadMessageImage(imageUri: Uri): Result<String> {
        return try {
            val imageRef = storage.reference.child("message_images/${UUID.randomUUID()}.jpg")
            val uploadTask = imageRef.putFile(imageUri).await()
            val downloadUrl = uploadTask.storage.downloadUrl.await()
            Result.success(downloadUrl.toString())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun observeMessages(chatId: String): Flow<List<Message>> = callbackFlow {
        val listener = messagesCollection
            .whereEqualTo("chatId", chatId)
            .orderBy("timestamp", Query.Direction.ASCENDING)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val messages = snapshot?.documents?.mapNotNull { document ->
                    document.toObject(Message::class.java)?.copy(id = document.id)
                } ?: emptyList()
                
                trySend(messages)
            }
        
        awaitClose { listener.remove() }
    }
    
    fun observeChats(): Flow<List<Chat>> = callbackFlow {
        val currentUserId = auth.currentUser?.uid
        if (currentUserId == null) {
            close(Exception("User not authenticated"))
            return@callbackFlow
        }
        
        val listener = chatsCollection
            .whereArrayContains("participants", currentUserId)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    close(error)
                    return@addSnapshotListener
                }
                
                val chats = snapshot?.documents?.mapNotNull { document ->
                    document.toObject(Chat::class.java)?.copy(id = document.id)
                }?.sortedByDescending { chat ->
                    // Sort by last message timestamp (client-side sorting)
                    chat.lastMessage?.timestamp?.time ?: 0L
                } ?: emptyList()

                trySend(chats)
            }
        
        awaitClose { listener.remove() }
    }
    
    suspend fun markMessageAsRead(messageId: String): Result<Unit> {
        return try {
            messagesCollection.document(messageId).update("isRead", true).await()
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun getUnreadMessageCount(chatId: String): Result<Int> {
        return try {
            val currentUserId = auth.currentUser?.uid ?: throw Exception("User not authenticated")
            
            val snapshot = messagesCollection
                .whereEqualTo("chatId", chatId)
                .whereEqualTo("receiverId", currentUserId)
                .whereEqualTo("isRead", false)
                .get()
                .await()
            
            Result.success(snapshot.size())
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
